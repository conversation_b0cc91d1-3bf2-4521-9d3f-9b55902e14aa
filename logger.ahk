#Requires AutoHotkey v2.0

; 日志记录模块
; 管理技能释放日志的记录和文件操作

global logFile := A_ScriptDir . "\skill_log.txt"		; 日志文件路径
global maxLogSize := 1048576							; 最大日志文件大小 (1MB)
global enableLogging := true							; 是否启用日志记录

; 记录技能释放日志
LogSkillRelease(skillKey, skillName) {
	global logFile, enableLogging
	
	if !enableLogging {
		return
	}
	
	try {
		; 检查日志文件大小，如果过大则清理
		CheckAndRotateLog()
		
		; 格式化时间戳
		timestamp := FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss.fff")
		
		; 构建日志条目
		logEntry := Format("[{1}] 技能释放: {2}({3})`n", timestamp, skillName, skillKey)
		
		; 写入日志文件
		FileAppend(logEntry, logFile, "UTF-8")
		
	} catch OSError as e {
		; 日志写入失败时显示提示（可选）
		; ToolTip(Format("日志写入失败: {1}", e.Message))
		; SetTimer(() => ToolTip(), -1000)
	} catch Error as e {
		; 静默处理其他错误
	}
}

; 记录脚本状态变化日志
LogStatusChange(status, message := "") {
	global logFile, enableLogging
	
	if !enableLogging {
		return
	}
	
	try {
		CheckAndRotateLog()
		
		timestamp := FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss.fff")
		
		logEntry := message != "" 
			? Format("[{1}] 状态变化: {2} - {3}`n", timestamp, status, message)
			: Format("[{1}] 状态变化: {2}`n", timestamp, status)
		
		FileAppend(logEntry, logFile, "UTF-8")
		
	} catch OSError as e {
		; 静默处理错误
	} catch Error as e {
		; 静默处理错误
	}
}

; 记录错误日志
LogError(errorType, errorMessage) {
	global logFile, enableLogging
	
	if !enableLogging {
		return
	}
	
	try {
		CheckAndRotateLog()
		
		timestamp := FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss.fff")
		logEntry := Format("[{1}] 错误: {2} - {3}`n", timestamp, errorType, errorMessage)
		
		FileAppend(logEntry, logFile, "UTF-8")
		
	} catch OSError as e {
		; 静默处理错误
	} catch Error as e {
		; 静默处理错误
	}
}

; 检查并轮转日志文件
CheckAndRotateLog() {
	global logFile, maxLogSize
	
	try {
		if !FileExist(logFile) {
			return
		}
		
		; 获取文件大小
		fileSize := FileGetSize(logFile)
		
		if fileSize > maxLogSize {
			; 备份当前日志文件
			backupFile := StrReplace(logFile, ".txt", "_backup.txt")
			
			; 删除旧备份文件（如果存在）
			if FileExist(backupFile) {
				FileDelete(backupFile)
			}
			
			; 重命名当前日志文件为备份文件
			FileMove(logFile, backupFile)
			
			; 在新日志文件中记录轮转信息
			timestamp := FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss.fff")
			rotateEntry := Format("[{1}] 日志轮转: 旧日志已备份为 {2}`n", timestamp, backupFile)
			FileAppend(rotateEntry, logFile, "UTF-8")
		}
		
	} catch OSError as e {
		; 静默处理错误
	} catch Error as e {
		; 静默处理错误
	}
}

; 打开日志文件查看
OpenLogFile() {
	global logFile
	
	try {
		if FileExist(logFile) {
			Run(logFile)
		} else {
			MsgBox("日志文件不存在: " . logFile, "提示", 64)
		}
	} catch OSError as e {
		MsgBox(Format("无法打开日志文件: {1}", e.Message), "错误", 16)
	} catch Error as e {
		MsgBox("打开日志文件失败", "错误", 16)
	}
}

; 清空日志文件
ClearLogFile() {
	global logFile
	
	try {
		if FileExist(logFile) {
			FileDelete(logFile)
			
			; 创建新的日志文件并记录清空操作
			timestamp := FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss.fff")
			clearEntry := Format("[{1}] 日志清空: 日志文件已手动清空`n", timestamp)
			FileAppend(clearEntry, logFile, "UTF-8")
			
			MsgBox("日志文件已清空", "提示", 64)
		} else {
			MsgBox("日志文件不存在", "提示", 64)
		}
	} catch OSError as e {
		MsgBox(Format("清空日志文件失败: {1}", e.Message), "错误", 16)
	} catch Error as e {
		MsgBox("清空日志文件失败", "错误", 16)
	}
}

; 切换日志记录状态
ToggleLogging() {
	global enableLogging, logFile
	
	enableLogging := !enableLogging
	
	try {
		timestamp := FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss.fff")
		statusText := enableLogging ? "启用" : "禁用"
		toggleEntry := Format("[{1}] 日志设置: 日志记录已{2}`n", timestamp, statusText)
		
		if enableLogging {
			FileAppend(toggleEntry, logFile, "UTF-8")
		}
		
		ToolTip(Format("日志记录已{1}", statusText))
		SetTimer(() => ToolTip(), -2000)
		
	} catch OSError as e {
		; 静默处理错误
	} catch Error as e {
		; 静默处理错误
	}
	
	return enableLogging
}

; 获取日志记录状态
IsLoggingEnabled() {
	global enableLogging
	return enableLogging
}

; 初始化日志系统
InitializeLogger() {
	global logFile, enableLogging
	
	try {
		; 如果日志文件不存在，创建并写入初始化信息
		if !FileExist(logFile) {
			timestamp := FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss.fff")
			initEntry := Format("[{1}] 系统启动: 技能助手日志系统初始化`n", timestamp)
			FileAppend(initEntry, logFile, "UTF-8")
		} else {
			; 如果文件存在，记录系统重启
			timestamp := FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss.fff")
			restartEntry := Format("[{1}] 系统重启: 技能助手重新启动`n", timestamp)
			FileAppend(restartEntry, logFile, "UTF-8")
		}
		
		return true
		
	} catch OSError as e {
		enableLogging := false
		return false
	} catch Error as e {
		enableLogging := false
		return false
	}
}
